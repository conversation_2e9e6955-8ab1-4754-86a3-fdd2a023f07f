import { useState } from "react";
import { motion } from "framer-motion";
import { Mail, CheckCircle, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface ContactFormProps {
  title?: string;
  description?: string;
  className?: string;
}

export default function ContactForm({
  title = "Get in Touch",
  description = "Send us a message and we'll get back to you within 24 hours.",
  className = "",
}: ContactFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name || !formData.email || !formData.message) return;

    setIsSubmitting(true);
    setError("");

    try {
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to send message");
      }

      setIsSubmitted(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to send message");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    setFormData((prev) => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
  };

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className={`bg-white/70 backdrop-blur-sm rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.06)] p-8 border border-white/20 text-center ${className}`}
      >
        <div className="flex items-center justify-center mb-6">
          <div className="bg-[#B8FF5C]/20 p-3 rounded-full">
            <CheckCircle className="w-6 h-6 text-[#0C1C2D]" />
          </div>
        </div>

        <h2 className="text-2xl font-bold text-gray-900 mb-4">Message Sent!</h2>

        <div className="flex items-center justify-center mb-4">
          <div
            className="w-12 h-1 rounded-full"
            style={{ backgroundColor: "#B8FF5C" }}
          ></div>
        </div>

        <p className="text-gray-600 mb-6">
          Thanks {formData.name}! We received your message and will get back to
          you at <strong>{formData.email}</strong> within 24 hours.
        </p>

        <Button
          onClick={() => {
            setIsSubmitted(false);
            setFormData({ name: "", email: "", message: "" });
          }}
          className="bg-[#1EAEDB] hover:bg-[#1EAEDB]/90 text-white px-6 py-3 border-0"
          style={{ backgroundColor: "#1EAEDB" }}
        >
          Send Another Message
        </Button>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className={`bg-white/70 backdrop-blur-sm rounded-2xl shadow-[0_8px_32px_rgba(0,0,0,0.06)] p-8 border border-white/20 ${className}`}
    >
      <div className="flex items-center justify-center mb-6">
        <div className="bg-[#1EAEDB]/10 p-3 rounded-full">
          <Mail className="w-6 h-6 text-[#1EAEDB]" />
        </div>
      </div>

      <h2 className="text-2xl font-bold text-gray-900 mb-2 text-center">
        {title}
      </h2>
      <p className="text-gray-600 mb-6 text-center">{description}</p>

      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
          <span className="text-red-700 text-sm">{error}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label
            htmlFor="name"
            className="block text-sm font-medium text-gray-700 mb-1 text-left"
          >
            Name
          </label>
          <Input
            id="name"
            name="name"
            type="text"
            placeholder="Your full name"
            value={formData.name}
            onChange={handleInputChange}
            required
            className="w-full px-4 py-3 text-lg border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#1EAEDB] focus:border-[#1EAEDB] transition-colors"
          />
        </div>

        <div>
          <label
            htmlFor="email"
            className="block text-sm font-medium text-gray-700 mb-1 text-left"
          >
            Email Address
          </label>
          <Input
            id="email"
            name="email"
            type="email"
            placeholder="<EMAIL>"
            value={formData.email}
            onChange={handleInputChange}
            required
            className="w-full px-4 py-3 text-lg border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#1EAEDB] focus:border-[#1EAEDB] transition-colors"
          />
        </div>

        <div>
          <label
            htmlFor="message"
            className="block text-sm font-medium text-gray-700 mb-1 text-left"
          >
            Message
          </label>
          <textarea
            id="message"
            name="message"
            placeholder="Tell us how we can help you..."
            value={formData.message}
            onChange={handleInputChange}
            required
            rows={4}
            className="w-full px-4 py-3 text-lg border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#1EAEDB] focus:border-[#1EAEDB] transition-colors resize-none"
          />
        </div>

        <button
          type="submit"
          disabled={
            isSubmitting ||
            !formData.name ||
            !formData.email ||
            !formData.message
          }
          style={{
            backgroundColor:
              isSubmitting ||
              !formData.name ||
              !formData.email ||
              !formData.message
                ? "#87ceeb"
                : "#1EAEDB",
            color: "white",
            width: "100%",
            padding: "12px 16px",
            fontSize: "18px",
            fontWeight: "600",
            borderRadius: "6px",
            border: "none",
            cursor:
              isSubmitting ||
              !formData.name ||
              !formData.email ||
              !formData.message
                ? "not-allowed"
                : "pointer",
            transition: "all 0.2s",
          }}
        >
          {isSubmitting ? (
            <div className="flex items-center justify-center">
              <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
              Sending...
            </div>
          ) : (
            "Send Message"
          )}
        </button>
      </form>

      <div className="flex items-center justify-center mt-4">
        <div
          className="w-8 h-1 rounded-full"
          style={{ backgroundColor: "#B8FF5C" }}
        ></div>
      </div>
    </motion.div>
  );
}
