{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "tsc", "db:push": "drizzle-kit push", "prepare": "husky install", "lint": "eslint 'client/src/**/*.{ts,tsx}' 'api/**/*.{ts,tsx}' 'shared/**/*.{ts,tsx}'", "format": "prettier --write .", "format:check": "prettier --check .", "typecheck": "tsc --noEmit", "test": "vitest", "test:ci": "vitest run --coverage", "analyse": "vite build"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@jridgewell/trace-mapping": "^0.3.25", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@sendgrid/mail": "^8.1.5", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.60.5", "@vercel/node": "^5.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "date-fns": "^3.6.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express-session": "^1.18.1", "framer-motion": "^11.18.2", "input-otp": "^1.4.2", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "nanoid": "^5.1.5", "next-themes": "^0.4.6", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "resend": "^4.5.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@eslint/js": "^9.27.0", "@replit/vite-plugin-cartographer": "^0.2.5", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/connect-pg-simple": "^7.0.3", "@types/express-session": "^1.18.0", "@types/jest": "^29.5.14", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/supertest": "^6.0.3", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-react": "^4.3.2", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.31.1", "eslint": "^9.27.0", "globals": "^16.2.0", "husky": "^9.1.7", "jscodeshift": "^17.3.0", "jsdom": "^26.1.0", "lint-staged": "^16.1.0", "postcss": "^8.4.47", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^6.0.1", "sharp": "^0.34.2", "sharp-cli": "^5.1.0", "supertest": "^7.1.1", "tailwindcss": "^3.4.17", "typescript": "5.6.3", "vite": "^6.3.5", "vite-imagetools": "^7.1.0", "vite-plugin-bundlesize": "^0.2.0", "vitest": "^3.1.4"}, "optionalDependencies": {"bufferutil": "^4.0.8"}, "pnpm": {"overrides": {"esbuild": "^0.25.0"}}}